import React, { useState, useEffect, useCallback } from 'react';
import { useLocation, Link } from 'react-router-dom';
import {
  Home, Info, Briefcase, Phone, FolderKanban, Users, Package,
  MessageSquare, X as Close, Menu, ExternalLink, Shield, Sparkles, ChevronRight
} from 'lucide-react';
import logo from '../assets/SipherWebLogo.png';

const menuItems = [
  { name: 'Home', path: '/home', icon: Home, description: 'Back to homepage' },
  { name: 'About', path: '/about', icon: Info, description: 'Learn about our company' },
  { name: 'Services', path: '/services', icon: Briefcase, description: 'Explore our solutions' },
  { name: 'Portfolio', path: '/portfolio', icon: FolderKanban, description: 'View our work' },
  { name: 'Products', path: '/products', icon: Package, description: 'Browse our products' },
  { name: 'Community', path: '/community', icon: Users, description: 'Join our network' },
  { name: 'Packages', path: '/packages', icon: Package, description: 'Explore our packages' },
  { name: 'Contact', path: '/contact', icon: MessageSquare, description: 'Get in touch' }
];

const Header = React.memo(() => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  const toggleMenu = useCallback(() => setIsOpen(prev => !prev), []);
  const closeMenu = useCallback(() => setIsOpen(false), []);
  const isActiveLink = useCallback((path) => location.pathname === path, [location.pathname]);

  const handleNavLinkClick = useCallback(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    closeMenu();
  }, [closeMenu]);

  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 50);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className={`fixed top-0 w-full z-40 transition-all duration-500 ${isScrolled ? 'bg-white/90 backdrop-blur-md shadow-lg' : 'bg-white'}`}>
      {/* Top Information Bar */}
      <div className="hidden lg:block bg-gradient-to-r from-[#100562] via-blue-600 to-[#100562] text-white py-2 px-4 text-center text-sm">
        <div className="container mx-auto flex items-center justify-center gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            <span className="text-xs font-medium">Trusted by <span className="text-[#FFE300]">100+</span> Enterprise Clients Worldwide</span>
            <ExternalLink className="w-4 h-4" />
          </div>
          <div className="flex items-center gap-2 border-l border-white pl-4">
            <Sparkles className="w-4 h-4" />
            <span className="text-xs font-medium">Delivering <span className="text-[#FFE300]">Innovation</span> & <span className="text-[#FFE300]">Excellence</span> Since 2022</span>
          </div>
          <div className="flex items-center gap-2 border-l border-white pl-4">
            <Users className="w-4 h-4" />
            <span className="text-xs font-medium">Join Our <span className="text-[#FFE300]">Growing Community</span> of Tech Enthusiasts</span>
          </div>
          <div className="flex items-center gap-2 border-l border-white pl-4">
            <Phone className="w-4 h-4" />
            <span className="text-xs font-medium">Contact Us: <span className="text-[#FFE300]">9125545607</span></span>
          </div>
        </div>
      </div>

      <nav className="container mx-auto flex justify-between items-center px-4 lg:px-8">
        {/* Logo */}
        <Link to="/home" onClick={handleNavLinkClick} className="relative group">
          <img src={logo} alt="Sipher Web Logo" className="h-14 w-auto transform group-hover:scale-105 transition-all duration-300" />
        </Link>

        {/* Desktop Menu */}
        <div className="hidden lg:flex items-center space-x-3">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center px-3 py-2 font-medium transition-all duration-300 rounded-lg ${isActiveLink(item.path) ? 'text-[#FFE300]' : 'text-gray-700 hover:text-[#100562]'}`}
              aria-label={item.description}
              onClick={handleNavLinkClick}
            >
              <item.icon className="w-4 h-4 mr-2" />
              <span>{item.name}</span>
              {isActiveLink(item.path) && <ChevronRight className="w-4 h-4 text-[#FFF300] ml-2" strokeWidth={5} />}
            </Link>
          ))}

          <a href="/contact" className="ml-4 px-5 py-2 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg" onClick={handleNavLinkClick}>
            Get Started
          </a>
        </div>

        {/* Mobile Menu Button */}
        <button onClick={toggleMenu} className="lg:hidden p-2 rounded-lg transition-all hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#100562]" aria-label="Toggle menu">
          {isOpen ? <Close className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </nav>

      {/* Mobile Menu */}
      {isOpen && (
        <div className="lg:hidden absolute top-full left-0 w-full bg-white shadow-lg z-50">
          <ul className="flex flex-col space-y-2 p-4">
            {menuItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className="flex items-center space-x-3 p-2 text-gray-700 hover:text-[#100562] hover:bg-gray-100 rounded-lg transition-all"
                  onClick={handleNavLinkClick}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              </li>
            ))}
            <li>
              <a href="/contact" className="block text-center px-5 py-2 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all" onClick={handleNavLinkClick}>
                Get Started
              </a>
            </li>
          </ul>
        </div>
      )}
    </header>
  );
});

export default Header;
