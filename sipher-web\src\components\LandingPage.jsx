import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Helmet } from 'react-helmet';
import {
  ArrowRight,
  MessageCircle,
  Code,
  GraduationCap,
  BookOpen,
  Briefcase,
  MapPin,
  Phone,
  Mail,
  ExternalLink,
  ChevronRight,
  Smartphone,
  Server,
  BarChart,
  Palette,
  Sun,
  Factory,
  Snowflake,
  Building
} from 'lucide-react';
import logo from '../assets/SipherWebLogo.png';

const LandingPage = () => {
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);

    // Add animation class to body for page transitions
    document.body.classList.add('page-loaded');
    return () => document.body.classList.remove('page-loaded');
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      {/* SEO Meta Tags */}
      <Helmet>
        <title>Sipher Web - IT Services & Technical Training</title>
        <meta name="description" content="Sipher Web offers professional IT development services and comprehensive technical training programs. Visit our gateway to explore both our development and training websites." />
        <meta name="keywords" content="web development, app development, software development, technical training, IT services, Sipher Web, digital marketing, UI/UX design" />
        <meta property="og:title" content="Sipher Web - IT Services & Technical Training" />
        <meta property="og:description" content="Professional IT development services and comprehensive technical training programs by Sipher Web." />
        <meta property="og:image" content="https://www.sipherwebtech.com/static/media/SipherWebLogo.a1c2c2c5c2c5c2c5c2c5.png" />
        <meta property="og:url" content="https://www.sipherwebtech.com" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://www.sipherwebtech.com" />
      </Helmet>
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-16 pb-24">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5 z-0">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, #100562 1px, transparent 0)`,
            backgroundSize: '24px 24px'
          }}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Logo and Company Name */}
          <div className="flex flex-col items-center text-center mb-16">
            <motion.img
              src={logo}
              alt="Sipher Web Logo"
              className="h-24 w-auto mb-6"
              initial={{ y: -50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6 }}
            />
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#100562] mb-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Sipher Web Pvt Ltd<span className="text-[#FFE300]">.</span>
            </motion.h1>
            <motion.p
              className="text-lg md:text-xl text-gray-600 max-w-3xl"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              Empowering businesses with cutting-edge web solutions and comprehensive tech training programs.
            </motion.p>
          </div>

          {/* Two Main Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
             {/* Training Section */}
             <motion.div
              className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1"
              initial={{ x: 50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <div className="h-2 bg-[#FFE300]"></div>
              <div className="p-8">
                <div className="w-16 h-16 bg-yellow-50 rounded-2xl flex items-center justify-center mb-6">
                  <GraduationCap className="w-8 h-8 text-[#FFE300]" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Technical Training Programs</h2>
                <p className="text-gray-600 mb-6">
                  Enhance your skills with our specialized training programs. Learn from industry experts and gain practical knowledge that prepares you for real-world challenges in the tech industry.
                </p>

                {/* Website Preview */}
                <div className="mb-8 rounded-lg overflow-hidden shadow-md border border-gray-200 hover:shadow-lg transition-all duration-300">
                  <div className="bg-gray-800 px-4 py-2 flex items-center gap-2">
                    <div className="flex gap-1.5">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-xs text-gray-300 flex-1 text-center">www.sipherweb.com</div>
                  </div>
                  <div className="relative">
                    <div className="aspect-video relative overflow-hidden">
                      {/* Actual website screenshot */}
                      <img
                        src="https://i.imgur.com/8fPXGjL.png"
                        alt="Sipher Web Training Preview"
                        className="w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-700"
                        loading="lazy"
                        onError={(e) => {
                          e.target.onerror = null;
                          // Fallback to gradient with logo if image fails to load
                          e.target.parentNode.classList.add('bg-gradient-to-b', 'from-[#FFE300]/5', 'to-[#FFE300]/10', 'flex', 'items-center', 'justify-center');
                          e.target.src = logo;
                          e.target.className = 'h-16 w-auto object-contain';
                        }}
                      />
                      {/* Overlay with subtle yellow tint */}
                      <div className="absolute inset-0 bg-[#FFE300]/5 hover:bg-transparent transition-colors duration-300"></div>
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300">
                      <a
                        href="https://sipherweb.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="opacity-0 hover:opacity-100 bg-[#FFE300] text-[#100562] px-4 py-2 rounded-lg flex items-center gap-2 transform scale-95 hover:scale-100 transition-all duration-300"
                      >
                        <span>Visit Website</span>
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </div>
                  </div>
                </div>

                <div className="space-y-3 mb-8">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Sun className="w-5 h-5 text-[#FFE300]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Summer Training</h3>
                      <p className="text-gray-600">Intensive programs during summer break for students</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Briefcase className="w-5 h-5 text-[#FFE300]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Apprenticeship Program</h3>
                      <p className="text-gray-600">Learn while working on real projects with our team</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Factory className="w-5 h-5 text-[#FFE300]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Industrial Training</h3>
                      <p className="text-gray-600">Industry-focused training for professional development</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Snowflake className="w-5 h-5 text-[#FFE300]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Winter Training</h3>
                      <p className="text-gray-600">Specialized programs during winter break</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <BookOpen className="w-5 h-5 text-[#FFE300]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Syllabus Training</h3>
                      <p className="text-gray-600">Curriculum-aligned programs for academic enhancement</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Building className="w-5 h-5 text-[#FFE300]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Corporate Training</h3>
                      <p className="text-gray-600">Customized training solutions for businesses</p>
                    </div>
                  </div>
                </div>
                <a
                  href="https://sipherweb.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-[#FFE300] text-[#100562] rounded-lg hover:bg-yellow-400 transition-all duration-300 group"
                >
                  <span>Visit Training Website</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </a>
              </div>
            </motion.div>

            {/* Development Section */}
            <motion.div
              className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1"
              initial={{ x: -50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="h-2 bg-[#100562]"></div>
              <div className="p-8">
                <div className="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mb-6">
                  <Code className="w-8 h-8 text-[#100562]" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-4">IT & Development Services</h2>
                <p className="text-gray-600 mb-6">
                  Transform your digital presence with our comprehensive IT solutions. We deliver custom, responsive, and high-performing digital products tailored to your business needs.
                </p>

                {/* Website Preview */}
                <div className="mb-8 rounded-lg overflow-hidden shadow-md border border-gray-200 hover:shadow-lg transition-all duration-300">
                  <div className="bg-gray-800 px-4 py-2 flex items-center gap-2">
                    <div className="flex gap-1.5">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-xs text-gray-300 flex-1 text-center">www.sipherwebtech.com</div>
                  </div>
                  <div className="relative">
                    <div className="aspect-video relative overflow-hidden">
                      {/* Actual website screenshot */}
                      <img
                        src="https://i.imgur.com/JLhRwpU.png"
                        alt="Sipher Web Tech Preview"
                        className="w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-700"
                        loading="lazy"
                        onError={(e) => {
                          e.target.onerror = null;
                          // Fallback to gradient with logo if image fails to load
                          e.target.parentNode.classList.add('bg-gradient-to-b', 'from-[#100562]/5', 'to-[#100562]/10', 'flex', 'items-center', 'justify-center');
                          e.target.src = logo;
                          e.target.className = 'h-16 w-auto object-contain';
                        }}
                      />
                      {/* Overlay with subtle blue tint */}
                      <div className="absolute inset-0 bg-[#100562]/5 hover:bg-transparent transition-colors duration-300"></div>
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300">
                      <a
                        href="https://www.sipherwebtech.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="opacity-0 hover:opacity-100 bg-[#100562] text-white px-4 py-2 rounded-lg flex items-center gap-2 transform scale-95 hover:scale-100 transition-all duration-300"
                      >
                        <span>Visit Website</span>
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </div>
                  </div>
                </div>

                <div className="space-y-3 mb-8">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Code className="w-5 h-5 text-[#100562]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Web Development</h3>
                      <p className="text-gray-600">Custom websites, e-commerce platforms, and web applications</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Smartphone className="w-5 h-5 text-[#100562]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Application Development</h3>
                      <p className="text-gray-600">Mobile apps, cross-platform solutions, and progressive web apps</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Server className="w-5 h-5 text-[#100562]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Software Development</h3>
                      <p className="text-gray-600">Custom software solutions, enterprise applications, and APIs</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <BarChart className="w-5 h-5 text-[#100562]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">Digital Marketing</h3>
                      <p className="text-gray-600">SEO, social media marketing, and content strategy</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <Palette className="w-5 h-5 text-[#100562]" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-800">UI/UX Design</h3>
                      <p className="text-gray-600">User-centered design, wireframing, and prototyping</p>
                    </div>
                  </div>
                </div>
                <a
                  href="https://www.sipherwebtech.com/home"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300 group"
                >
                  <span>Visit Development Website</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </a>
              </div>
            </motion.div>

          </div>

          {/* About Section */}
          <motion.div
            className="bg-white rounded-2xl shadow-lg p-8 mb-16"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1 }}
          >
            <h2 className="text-2xl font-bold text-gray-800 mb-4">About Sipher Web</h2>
            <p className="text-gray-600 mb-6">
              Founded in 2022, Sipher Web has quickly established itself as a leader in web development and technology training.
              Our mission is to empower businesses and individuals with the tools and knowledge they need to succeed in the digital world.
            </p>
            <p className="text-gray-600 mb-6">
              With a team of experienced professionals and a commitment to excellence, we deliver innovative solutions and comprehensive training programs that meet the highest standards of quality and effectiveness.
            </p>

            {/* Map Section */}
                  <motion.div
                    className="mt-8"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                    viewport={{ once: true, margin: "-100px" }}
                  >
                    <motion.h3
                      className="text-xl font-semibold text-gray-800 mb-4 flex items-center"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      viewport={{ once: true }}
                    >
                      <MapPin className="w-5 h-5 text-[#100562] mr-2" />
                      Our Location
                    </motion.h3>

                    <motion.div
                      className="rounded-xl overflow-hidden shadow-md border border-gray-200 relative"
                      initial={{ opacity: 0, scale: 0.95 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.4 }}
                      viewport={{ once: true }}
                      whileHover={{ boxShadow: "0 10px 25px -5px rgba(16, 5, 98, 0.1), 0 8px 10px -6px rgba(16, 5, 98, 0.1)" }}
                    >
                      <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3559.6151333205323!2d80.958089!3d26.9094446!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x399957c122281889%3A0xfc548cb147027514!2sSipher%20Web%20Pvt%20Ltd!5e0!3m2!1sen!2sin!4v1698308833797!5m2!1sen!2sin"
                        width="100%"
                        height="300"
                        style={{ border: 0 }}
                        allowFullScreen=""
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                        title="Sipher Web Location"
                      ></iframe>

                      {/* Map overlay with pulsing marker */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
                        <div className="w-6 h-6 bg-[#100562] rounded-full flex items-center justify-center">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        </div>
                        <div className="w-16 h-16 bg-[#100562]/20 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-ping opacity-75"></div>
                      </div>
                    </motion.div>

                    <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                      <motion.div
                        className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 flex items-start gap-3 hover:shadow-md transition-shadow duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.5 }}
                        viewport={{ once: true }}
                        whileHover={{ y: -5 }}
                      >
                        <MapPin className="w-5 h-5 text-[#100562] flex-shrink-0 mt-1" />
                        <div>
                          <h4 className="font-medium text-gray-800">Address</h4>
                          <p className="text-sm text-gray-600">First Floor, C-1/364, Near Muglai Hutz, Sector G, Jankipuram, Tedhi Pulia, Lucknow, India</p>
                        </div>
                      </motion.div>

                      <motion.div
                        className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 flex items-start gap-3 hover:shadow-md transition-shadow duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.6 }}
                        viewport={{ once: true }}
                        whileHover={{ y: -5 }}
                      >
                        <Phone className="w-5 h-5 text-[#100562] flex-shrink-0 mt-1" />
                        <div>
                          <h4 className="font-medium text-gray-800">Phone</h4>
                          <a href="tel:+************" className="text-sm text-gray-600 hover:text-[#100562] transition-colors">+91 91255 45607</a>
                        </div>
                      </motion.div>

                      <motion.div
                        className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 flex items-start gap-3 hover:shadow-md transition-shadow duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.7 }}
                        viewport={{ once: true }}
                        whileHover={{ y: -5 }}
                      >
                        <Mail className="w-5 h-5 text-[#100562] flex-shrink-0 mt-1" />
                        <div>
                          <h4 className="font-medium text-gray-800">Email</h4>
                          <a href="mailto:<EMAIL>" className="text-sm text-gray-600 hover:text-[#100562] transition-colors"><EMAIL></a>
                        </div>
                      </motion.div>
                    </div>

                    <motion.div
                      className="mt-6 flex justify-center"
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.8 }}
                      viewport={{ once: true }}
                    >
                      <motion.a
                        href="https://www.google.com/maps/dir//Sipher+Web+Pvt+Ltd+Ground+Floor+C-1%2F364,+near+Muglai+hutz+Tedhi+Pulia,+Sector+G,+Uttar+Pradesh+226021/@26.9143268,80.9245122,12.8z/data=!4m8!4m7!1m0!1m5!1m1!1s0x399957c122281889:0xfc548cb147027514!2m2!1d80.958089!2d26.9094446?entry=ttu&g_ep=EgoyMDI0MTAyMS4xIKXMDSoASAFQAw%3D%3D"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-2 px-5 py-2 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300 text-sm group"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span>Get Directions</span>
                        <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </motion.a>
                    </motion.div>
                  </motion.div>
                  </motion.div>

                  {/* Social Media Links */}
                  <motion.div
                  className="flex flex-col items-center"
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                  >
                  <h3 className="text-xl font-semibold text-gray-800 mb-4">Connect With Us</h3>
                  <div className="flex gap-4">
                    <a
                    href="https://www.linkedin.com/company/sipher-web-private-limited/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-3 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300"
                    aria-label="LinkedIn"
                    >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
                      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                      <rect x="2" y="9" width="4" height="12"></rect>
                      <circle cx="4" cy="4" r="2"></circle>
                    </svg>
                    </a>
                    <a
                    href="https://www.instagram.com/sipherweb/profilecard/?igsh=eHBlZXllcWs4cW56"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-3 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300"
                    aria-label="Instagram"
                    >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
                      <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                      <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                      <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                    </svg>
                    </a>
                    
                    <a
                    href="https://www.youtube.com/@sipherweb"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-3 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300"
                    aria-label="YouTube"
                    >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
                      <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path>
                      <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>
                    </svg>
                    </a>
                    <a
                    href="https://wa.me/************"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-3 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300"
                    aria-label="WhatsApp"
                    >
                    <MessageCircle className="w-5 h-5" />
                    </a>
                    <a
                    href="https://www.facebook.com/sipherweb"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-3 bg-[#100562] text-white rounded-lg hover:bg-blue-700 transition-all duration-300"
                    aria-label="Facebook"
                    >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
                      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                    </svg>
                    </a>
                  </div>
                  </motion.div>
                </div>
                </section>

                {/* Footer */}
      <div className="bg-gray-50 py-6 text-center text-gray-600 text-sm">
        © {new Date().getFullYear()} Sipher Web Pvt Ltd. All rights reserved.
      </div>
    </div>
  );
};

export default LandingPage;
